<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Loading Test</title>
    
    <style>
        :root {
            --primary-color: #1976d2;
            --secondary-color: #ff7200;
            --background-primary: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--background-primary);
            background-attachment: fixed;
            color: var(--text-primary);
            overflow-x: hidden;
        }

        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--background-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.8s ease;
            overflow: hidden;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .welcome-container {
            text-align: center;
            position: relative;
            z-index: 10;
            animation: welcomeFadeIn 1.5s ease-out;
        }

        .welcome-logo-section {
            margin-bottom: 3rem;
        }

        .welcome-logo {
            position: relative;
            display: inline-block;
            margin-bottom: 2rem;
        }

        .site-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            margin: 0 auto;
            animation: logoFloat 3s ease-in-out infinite;
        }

        .site-logo::before {
            content: '⚡';
            color: white;
        }

        .welcome-title {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 0.5rem;
            opacity: 0;
            animation: textSlideUp 1s ease-out 0.8s forwards;
        }

        .brand-name {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            opacity: 0;
            animation: textSlideUp 1s ease-out 1s forwards;
        }

        .welcome-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            font-weight: 300;
            opacity: 0;
            animation: textSlideUp 1s ease-out 1.2s forwards;
        }

        .loading-progress {
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            margin: 2rem auto 0;
            overflow: hidden;
            opacity: 0;
            animation: fadeIn 1s ease-out 1.5s forwards;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
            width: 0%;
            animation: progressLoad 2s ease-out 1.5s forwards;
        }

        .skip-loading-btn {
            position: absolute;
            bottom: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .skip-loading-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            display: none;
            padding: 100px 20px;
            text-align: center;
            min-height: 100vh;
        }

        .main-content.visible {
            display: block;
        }

        @keyframes welcomeFadeIn {
            0% {
                opacity: 0;
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes logoFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        @keyframes textSlideUp {
            0% {
                opacity: 0;
                transform: translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            0% {
                opacity: 0;
            }
            100% {
                opacity: 1;
            }
        }

        @keyframes progressLoad {
            0% {
                width: 0%;
            }
            100% {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Welcome Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="welcome-container">
            <div class="welcome-logo-section">
                <div class="welcome-logo">
                    <div class="site-logo"></div>
                </div>
                <h1 class="welcome-title">Welcome to</h1>
                <h2 class="brand-name">Energy.AI</h2>
                <p class="welcome-subtitle">Smart Energy Solutions Powered by AI</p>
            </div>
            <div class="welcome-animation">
                <div class="loading-progress">
                    <div class="progress-bar"></div>
                </div>
                <button class="skip-loading-btn" onclick="skipLoading()">
                    Skip ⏭️
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <h1>Energy.AI Dashboard</h1>
        <p>Welcome to the main application!</p>
        <p>The loading screen should have disappeared automatically after 3 seconds, or you could have skipped it.</p>
    </div>

    <script>
        // Simple loading script
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded');

            let loadingHidden = false;

            function hideLoadingScreen() {
                if (loadingHidden) return;
                loadingHidden = true;

                console.log('Hiding loading screen...');
                const loadingScreen = document.getElementById('loadingScreen');
                const mainContent = document.getElementById('mainContent');
                
                if (loadingScreen) {
                    console.log('Loading screen found, adding hidden class');
                    loadingScreen.classList.add('hidden');
                    
                    setTimeout(() => {
                        if (loadingScreen.parentNode) {
                            console.log('Removing loading screen from DOM');
                            loadingScreen.remove();
                        }
                        if (mainContent) {
                            mainContent.classList.add('visible');
                        }
                    }, 800);
                } else {
                    console.log('Loading screen not found');
                }
            }

            // Hide after timeout (3 seconds)
            setTimeout(hideLoadingScreen, 3000);

            // Also hide when window is fully loaded
            window.addEventListener('load', () => {
                setTimeout(hideLoadingScreen, 1000);
            });

            // Make hideLoadingScreen globally available
            window.hideLoadingScreen = hideLoadingScreen;
        });

        // Skip loading function
        function skipLoading() {
            if (window.hideLoadingScreen) {
                window.hideLoadingScreen();
            }
        }
    </script>
</body>
</html>
