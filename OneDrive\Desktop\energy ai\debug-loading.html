<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Loading Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: white;
        }
        .debug-panel {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .log-entry {
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .error { color: #ff6b6b; }
        .success { color: #51cf66; }
        .info { color: #74c0fc; }
        .warning { color: #ffd43b; }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
        #testResults {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Energy.AI - Loading Screen Debug Tool</h1>
    
    <div class="debug-panel">
        <h2>System Status</h2>
        <div id="systemStatus">Initializing...</div>
    </div>

    <div class="debug-panel">
        <h2>Actions</h2>
        <button onclick="testLoadingScreen()">Test Loading Screen</button>
        <button onclick="checkMainIndex()">Check Main Index.html</button>
        <button onclick="runDiagnostics()">Run Full Diagnostics</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>

    <div class="debug-panel">
        <h2>Console Logs</h2>
        <div id="consoleLogs"></div>
    </div>

    <div id="testResults"></div>

    <script>
        let logs = [];

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logs.push({ timestamp, message, type });
            updateLogDisplay();
        }

        function updateLogDisplay() {
            const container = document.getElementById('consoleLogs');
            container.innerHTML = logs.map(log => 
                `<div class="log-entry ${log.type}">[${log.timestamp}] ${log.message}</div>`
            ).join('');
            container.scrollTop = container.scrollHeight;
        }

        function clearLogs() {
            logs = [];
            updateLogDisplay();
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('systemStatus');
            statusDiv.innerHTML = `<span class="${type}">${message}</span>`;
            addLog(`Status: ${message}`, type);
        }

        function testLoadingScreen() {
            addLog('Testing loading screen functionality...', 'info');
            
            // Test if loading screen elements exist
            const tests = [
                {
                    name: 'Loading Screen Element',
                    test: () => document.getElementById('loadingScreen') !== null
                },
                {
                    name: 'Welcome Container',
                    test: () => document.querySelector('.welcome-container') !== null
                },
                {
                    name: 'Skip Button',
                    test: () => document.querySelector('.skip-loading-btn') !== null
                },
                {
                    name: 'Progress Bar',
                    test: () => document.querySelector('.progress-bar') !== null
                }
            ];

            tests.forEach(test => {
                try {
                    const result = test.test();
                    addLog(`${test.name}: ${result ? 'PASS' : 'FAIL'}`, result ? 'success' : 'error');
                } catch (error) {
                    addLog(`${test.name}: ERROR - ${error.message}`, 'error');
                }
            });
        }

        function checkMainIndex() {
            addLog('Checking main index.html file...', 'info');
            
            fetch('index.html')
                .then(response => {
                    if (response.ok) {
                        addLog('Main index.html file is accessible', 'success');
                        return response.text();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(html => {
                    // Check for key elements in the HTML
                    const checks = [
                        { name: 'Loading Screen HTML', pattern: /id="loadingScreen"/ },
                        { name: 'Skip Loading Function', pattern: /function skipLoading/ },
                        { name: 'Hide Loading Function', pattern: /function hideLoadingScreen/ },
                        { name: 'Setup Energy Tool Sidebar', pattern: /setupEnergyToolSidebar/ }
                    ];

                    checks.forEach(check => {
                        const found = check.pattern.test(html);
                        addLog(`${check.name}: ${found ? 'FOUND' : 'MISSING'}`, found ? 'success' : 'warning');
                    });
                })
                .catch(error => {
                    addLog(`Failed to load index.html: ${error.message}`, 'error');
                });
        }

        function runDiagnostics() {
            addLog('Running full diagnostics...', 'info');
            
            // Check JavaScript errors
            window.addEventListener('error', function(e) {
                addLog(`JavaScript Error: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
            });

            // Check console errors
            const originalConsoleError = console.error;
            console.error = function(...args) {
                addLog(`Console Error: ${args.join(' ')}`, 'error');
                originalConsoleError.apply(console, args);
            };

            // Test CSS loading
            const stylesheets = document.styleSheets;
            addLog(`Found ${stylesheets.length} stylesheets`, 'info');

            // Test if main functions are available
            const functions = ['skipLoading', 'hideLoadingScreen', 'setupEnergyToolSidebar'];
            functions.forEach(funcName => {
                const exists = typeof window[funcName] === 'function';
                addLog(`Function ${funcName}: ${exists ? 'AVAILABLE' : 'MISSING'}`, exists ? 'success' : 'error');
            });

            // Test DOM ready state
            addLog(`Document ready state: ${document.readyState}`, 'info');
            
            updateStatus('Diagnostics completed', 'success');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('Debug tool loaded successfully', 'success');
            addLog('Debug tool initialized', 'success');
            
            // Auto-run basic diagnostics
            setTimeout(runDiagnostics, 1000);
        });

        // Capture any errors
        window.addEventListener('error', function(e) {
            addLog(`Global Error: ${e.message}`, 'error');
            updateStatus('Error detected - check logs', 'error');
        });
    </script>
</body>
</html>
